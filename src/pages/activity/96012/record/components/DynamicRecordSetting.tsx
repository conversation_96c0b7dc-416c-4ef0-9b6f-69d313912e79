import React, { useEffect, useState } from 'react';
import {
  Button,
  DatePicker2,
  Dialog,
  Field,
  Form,
  Icon,
  Input,
  Message,
  NumberPicker,
  Table,
  Balloon,
} from '@alifd/next';
import LzDialog from '@/components/LzDialog';
import LzPagination from '@/components/LzPagination';
import {
  dataShareLog, // 获得数据
  dataShareLogExport, // 导出数据
  dataShareLogRankTime, // 开启排名
  dataShareLogStartTime, // 设置开奖时间
  dataShareLogDelete, // 删除该条动态
  dataShareLogUpdateLike, // 修改人气值
  dataShareLogUpdateRank, // 修改晒照排名
} from '@/api/v96012';
import Utils, { downloadExcel, getParams } from '@/utils';
import constant from '@/utils/constant';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;

export default (props) => {
  const field = Field.useField();
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const defaultRangeVal = [
    dayjs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayjs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  // 分页信息
  const [pageInfo, setPageInfo] = useState({
    total: 0,
    pageNum: 1,
    pageSize: 20,
  });

  // 弹窗状态
  const [orderDetailVisible, setOrderDetailVisible] = useState(false);

  // 当前操作的数据
  const [orderDetailData, setOrderDetailData] = useState<any[]>([]);

  // 修改排名弹窗状态
  const [rankDialogVisible, setRankDialogVisible] = useState(false);
  // 修改人气值弹窗状态
  const [likeDialogVisible, setLikeDialogVisible] = useState(false);
  // 当前操作的行数据
  const [currentRowData, setCurrentRowData] = useState<any>(null);
  // 新排名值
  const [newRank, setNewRank] = useState<number>(0);
  // 新人气值
  const [newLike, setNewLike] = useState<number>(0);
  // 图片预览弹窗状态
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false);
  // 当前预览的图片列表
  const [previewImages, setPreviewImages] = useState<string[]>([]);

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    // 重置到第一页
    setPageInfo((prev) => ({ ...prev, pageNum: 1 }));
    loadData({ ...formValue, pageNum: 1, pageSize: pageInfo.pageSize });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.pageNum = query.pageNum || pageInfo.pageNum;
    query.pageSize = query.pageSize || pageInfo.pageSize;
    dataShareLog(query)
      .then((res: any): void => {
        setTableData(res.records || []);
        // 更新分页信息
        setPageInfo({
          total: parseInt(res.total || '0'),
          pageNum: parseInt(res.current || '1'),
          pageSize: parseInt(res.size || '20'),
        });
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };

  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataShareLogExport(formValue).then((data: any) => downloadExcel(data, '动态记录'));
  };

  // 分页处理
  const handlePageChange = ({ pageNum, pageSize }) => {
    setPageInfo((prev) => ({ ...prev, pageNum, pageSize }));
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageNum, pageSize });
  };

  // 删除动态
  const handleDelete = (rowData: any) => {
    Dialog.confirm({
      title: '确认删除',
      content: '确定要删除这条动态记录吗？删除后无法恢复。',
      onOk: async () => {
        try {
          setLoading(true);
          await dataShareLogDelete({
            activityId: getParams('id'),
            dynamicId: rowData.dynamicId,
            pageNum: pageInfo.pageNum,
            pageSize: pageInfo.pageSize,
          });
          Message.success('删除成功');
          // 重新加载数据，保持当前分页
          const formValue: any = field.getValues();
          loadData({ ...formValue, pageNum: pageInfo.pageNum, pageSize: pageInfo.pageSize });
        } catch (error: any) {
          Message.error(error?.message || '删除失败');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 打开修改排名弹窗
  const handleEditRank = (rowData: any) => {
    setCurrentRowData(rowData);
    setNewRank(rowData.rank || 0);
    setRankDialogVisible(true);
  };

  // 确认修改排名
  const handleConfirmRank = async () => {
    if (!newRank || newRank < 1) {
      Message.error('请输入有效的排名（大于0的整数）');
      return;
    }
    try {
      setLoading(true);
      await dataShareLogUpdateRank({
        activityId: getParams('id'),
        dynamicId: currentRowData.dynamicId,
        rank: newRank,
        pageNum: pageInfo.pageNum,
        pageSize: pageInfo.pageSize,
      });
      Message.success('排名修改成功');
      setRankDialogVisible(false);
      // 重新加载数据，保持当前分页
      const formValue: any = field.getValues();
      loadData({ ...formValue, pageNum: pageInfo.pageNum, pageSize: pageInfo.pageSize });
    } catch (error: any) {
      Message.error(error?.message || '排名修改失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开修改人气值弹窗
  const handleEditLike = (rowData: any) => {
    setCurrentRowData(rowData);
    setNewLike(rowData.fans || 0);
    setLikeDialogVisible(true);
  };

  // 确认修改人气值
  const handleConfirmLike = async () => {
    if (newLike < 0) {
      Message.error('请输入有效的人气值（大于等于0的整数）');
      return;
    }
    try {
      setLoading(true);
      await dataShareLogUpdateLike({
        activityId: getParams('id'),
        dynamicId: currentRowData.dynamicId,
        fans: newLike,
        pageNum: pageInfo.pageNum,
        pageSize: pageInfo.pageSize,
      });
      Message.success('人气值修改成功');
      setLikeDialogVisible(false);
      // 重新加载数据，保持当前分页
      const formValue: any = field.getValues();
      loadData({ ...formValue, pageNum: pageInfo.pageNum, pageSize: pageInfo.pageSize });
    } catch (error: any) {
      Message.error(error?.message || '人气值修改失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开图片预览弹窗
  const handleImagePreview = (images: string[]) => {
    setPreviewImages(images);
    setImagePreviewVisible(true);
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageNum: 1, pageSize: 20 });
  }, []);

  return (
    <div style={{ minHeight: '350px' }}>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="动态发布时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <Form.Item name="rank" label="用户排名">
          <Input placeholder="请输入用户排名" type={'number'} />
        </Form.Item>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              // 重置时回到第一页
              setPageInfo((prev) => ({ ...prev, pageNum: 1 }));
              const formValue: any = field.getValues();
              loadData({ ...formValue, pageNum: 1, pageSize: pageInfo.pageSize });
            }}
          >
            重置
          </Form.Reset>
          <Button onClick={exportData} style={{ marginRight: '8px' }}>
            导出
          </Button>
        </FormItem>
      </Form>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="发布时间" dataIndex="createTime" />
        <Table.Column title="排名" dataIndex="rank" />
        <Table.Column title="人气值" dataIndex="fans" />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.encryptPin ? Utils.mask(row.encryptPin) : '-'}
                  {row.encryptPin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.encryptPin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column title="父分类" dataIndex="fatherSection" width={150} />
        <Table.Column title="子分类" dataIndex="childSection" />
        <Table.Column title="标题" dataIndex="title" width={180} />
        <Table.Column
          title="内容"
          dataIndex="content"
          width={150}
          maxBodyHeight={30}
          cell={(value, index, data) => (
            <div style={{ maxWidth: '150px', overflow: 'hidden' }}>
              {value ? (
                <Balloon
                  trigger={<span style={{ cursor: 'pointer' }}>{value}</span>}
                  closable={false}
                  triggerType="hover"
                  align="br"
                  offset={[0, 0]}
                  autoAdjust
                  autoFocus
                >
                  {value}
                </Balloon>
              ) : (
                '-'
              )}
            </div>
          )}
        />
        <Table.Column
          title="图片信息"
          dataIndex="imgList"
          cell={(value, index, data) => (
            <div>
              {value && value.length > 0 ? (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <img
                    src={value[0]}
                    style={{
                      width: '60px',
                      height: '60px',
                      objectFit: 'cover',
                      cursor: 'pointer',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                    }}
                    onClick={() => handleImagePreview(value)}
                  />
                  {value.length > 1 && (
                    <span
                      style={{
                        marginLeft: '8px',
                        color: '#1890ff',
                        cursor: 'pointer',
                        fontSize: '12px',
                      }}
                      onClick={() => handleImagePreview(value)}
                    >
                      +{value.length - 1}张
                    </span>
                  )}
                </div>
              ) : (
                '-'
              )}
            </div>
          )}
        />
        <Table.Column
          title="操作"
          cell={(value, index, data) => (
            <div>
              <Button style={{ marginRight: '8px' }} text type="primary" onClick={() => handleDelete(data)}>
                删除
              </Button>
              <Button style={{ marginRight: '8px' }} text type="primary" onClick={() => handleEditRank(data)}>
                修改排名
              </Button>
              <Button style={{ marginRight: '8px' }} text type="primary" onClick={() => handleEditLike(data)}>
                修改人气值
              </Button>
            </div>
          )}
        />
      </Table>

      {/* 分页组件 */}
      <LzPagination
        total={pageInfo.total}
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        onChange={handlePageChange}
      />

      {/* 修改排名弹窗 */}
      <LzDialog
        title="修改排名"
        visible={rankDialogVisible}
        footer={false}
        onClose={() => setRankDialogVisible(false)}
        style={{ width: '400px' }}
      >
        <div style={{ padding: '20px' }}>
          <Form>
            <Form.Item label="当前排名：">
              <span>{currentRowData?.rank || '-'}</span>
            </Form.Item>
            <Form.Item label="新排名：" required>
              <NumberPicker
                value={newRank}
                onChange={(value: any) => setNewRank(value)}
                min={1}
                max={999999}
                style={{ width: '200px' }}
                placeholder="请输入新排名"
              />
            </Form.Item>
            <Form.Item style={{ textAlign: 'center', marginTop: '30px' }}>
              <Button type="primary" onClick={handleConfirmRank} style={{ marginRight: '10px' }}>
                确定
              </Button>
              <Button onClick={() => setRankDialogVisible(false)}>取消</Button>
            </Form.Item>
          </Form>
        </div>
      </LzDialog>

      {/* 修改人气值弹窗 */}
      <LzDialog
        title="修改人气值"
        visible={likeDialogVisible}
        footer={false}
        onClose={() => setLikeDialogVisible(false)}
        style={{ width: '400px' }}
      >
        <div style={{ padding: '20px' }}>
          <Form>
            <Form.Item label="当前人气值：">
              <span>{currentRowData?.fans || '-'}</span>
            </Form.Item>
            <Form.Item label="新人气值：" required>
              <NumberPicker
                value={newLike}
                onChange={(value: any) => setNewLike(value)}
                min={0}
                max={999999}
                style={{ width: '200px' }}
                placeholder="请输入新人气值"
              />
            </Form.Item>
            <Form.Item style={{ textAlign: 'center', marginTop: '30px' }}>
              <Button type="primary" onClick={handleConfirmLike} style={{ marginRight: '10px' }}>
                确定
              </Button>
              <Button onClick={() => setLikeDialogVisible(false)}>取消</Button>
            </Form.Item>
          </Form>
        </div>
      </LzDialog>

      {/* 图片预览弹窗 */}
      <LzDialog
        title="图片预览"
        visible={imagePreviewVisible}
        footer={false}
        onClose={() => setImagePreviewVisible(false)}
        style={{ width: '800px', maxWidth: '90vw' }}
      >
        <div style={{ padding: '20px', maxHeight: '70vh', overflow: 'auto' }}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
              gap: '16px',
            }}
          >
            {previewImages.map((image, index) => (
              <div
                key={index}
                style={{
                  textAlign: 'center',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                }}
              >
                <div
                  style={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: '150px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    backgroundColor: '#f9f9f9',
                  }}
                >
                  <img
                    src={image}
                    style={{
                      maxWidth: '100%',
                      maxHeight: '300px',
                      objectFit: 'contain',
                      borderRadius: '4px',
                    }}
                    alt={`图片 ${index + 1}`}
                  />
                </div>
                <div
                  style={{
                    marginTop: '8px',
                    fontSize: '12px',
                    color: '#666',
                    textAlign: 'center',
                    lineHeight: '1.2',
                    width: '100%',
                  }}
                >
                  图片 {index + 1}
                </div>
              </div>
            ))}
          </div>
        </div>
      </LzDialog>
    </div>
  );
};
